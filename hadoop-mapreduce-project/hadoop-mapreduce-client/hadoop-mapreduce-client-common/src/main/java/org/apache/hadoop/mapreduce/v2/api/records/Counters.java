/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hadoop.mapreduce.v2.api.records;

import java.util.Map;

public interface Counters {
  public abstract Map<String, CounterGroup> getAllCounterGroups();
  public abstract CounterGroup getCounterGroup(String key);
  public abstract Counter getCounter(Enum<?> key);
  
  public abstract void addAllCounterGroups(Map<String, CounterGroup> counterGroups);
  public abstract void setCounterGroup(String key, CounterGroup value);
  public abstract void removeCounterGroup(String key);
  public abstract void clearCounterGroups();
  
  public abstract void incrCounter(Enum<?> key, long amount);
}
