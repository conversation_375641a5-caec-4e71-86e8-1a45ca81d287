/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hadoop.yarn.server.resourcemanager;

import org.apache.hadoop.yarn.api.records.ApplicationId;
import org.apache.hadoop.yarn.event.AbstractEvent;

public class RMAppManagerEvent extends AbstractEvent<RMAppManagerEventType> {

  private final ApplicationId appId;
  private final String targetQueueForMove;

  public RMAppManagerEvent(ApplicationId appId, RMAppManagerEventType type) {
    this(appId, "", type);
  }

  public RMAppManagerEvent(ApplicationId appId, String targetQueueForMove,
      RMAppManagerEventType type) {
    super(type);
    this.appId = appId;
    this.targetQueueForMove = targetQueueForMove;
  }

  public ApplicationId getApplicationId() {
    return this.appId;
  }

  public String getTargetQueueForMove() {
    return this.targetQueueForMove;
  }
}
