/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hadoop.yarn.service;

import com.google.common.base.Preconditions;
import com.google.common.cache.LoadingCache;
import org.apache.hadoop.yarn.api.records.ApplicationAttemptId;
import org.apache.hadoop.yarn.security.client.ClientToAMTokenSecretManager;
import org.apache.hadoop.yarn.service.api.records.Service;
import org.apache.hadoop.yarn.service.api.records.ConfigFile;
import org.apache.hadoop.yarn.service.utils.SliderFileSystem;

import java.nio.ByteBuffer;

public class ServiceContext {
  public Service service = null;
  public SliderFileSystem fs;
  public String serviceHdfsDir = "";
  public ApplicationAttemptId attemptId;
  public LoadingCache<ConfigFile, Object> configCache;
  public ServiceScheduler scheduler;
  public ClientToAMTokenSecretManager secretManager;
  public ClientAMService clientAMService;
  // tokens used for container launch
  public ByteBuffer tokens;
  // AM keytab principal
  public String principal;
  // AM keytab location
  public String keytab;
  private ServiceManager serviceManager;

  public ServiceContext() {

  }

  public ServiceManager getServiceManager() {
    return serviceManager;
  }

  void setServiceManager(ServiceManager serviceManager) {
    this.serviceManager = Preconditions.checkNotNull(serviceManager);
  }

  public Service getService() {
    return service;
  }
}
