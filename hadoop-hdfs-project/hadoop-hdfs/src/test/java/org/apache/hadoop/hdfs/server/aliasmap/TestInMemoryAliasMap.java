/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.hadoop.hdfs.server.aliasmap;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hdfs.DFSConfigKeys;
import org.junit.Test;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

/**
 * TestInMemoryAliasMap tests the initialization of an AliasMap. Most of the
 * rest of the tests are in ITestInMemoryAliasMap since the tests are not
 * thread safe (there is competition for the port).
 */
public class TestInMemoryAliasMap {

  @Test
  public void testInit() {
    String nonExistingDirectory = "non-existing-directory";
    Configuration conf = new Configuration();
    conf.set(DFSConfigKeys.DFS_PROVIDED_ALIASMAP_INMEMORY_LEVELDB_DIR,
        nonExistingDirectory);

    assertThatExceptionOfType(IOException.class)
        .isThrownBy(() -> InMemoryAliasMap.init(conf, "bpid")).withMessage(
            InMemoryAliasMap.createPathErrorMessage(nonExistingDirectory));
  }
}