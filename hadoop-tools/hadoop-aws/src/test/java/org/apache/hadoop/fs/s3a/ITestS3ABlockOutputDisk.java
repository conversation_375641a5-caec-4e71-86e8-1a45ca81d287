/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hadoop.fs.s3a;

import org.junit.Assume;

/**
 * Use {@link Constants#FAST_UPLOAD_BUFFER_DISK} for buffering.
 */
public class ITestS3ABlockOutputDisk extends ITestS3ABlockOutputArray {

  protected String getBlockOutputBufferName() {
    return Constants.FAST_UPLOAD_BUFFER_DISK;
  }

  /**
   * The disk stream doesn't support mark/reset; calls
   * {@code Assume} to skip the test.
   * @param fileSystem source FS
   * @return null
   */
  protected S3ADataBlocks.BlockFactory createFactory(S3AFileSystem fileSystem) {
    Assume.assumeTrue("mark/reset nopt supoprted", false);
    return null;
  }
}
