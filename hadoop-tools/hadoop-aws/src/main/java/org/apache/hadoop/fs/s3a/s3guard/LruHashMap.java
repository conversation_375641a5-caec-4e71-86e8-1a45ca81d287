/*
 *  Licensed to the Apache Software Foundation (ASF) under one or more
 *  contributor license agreements.  See the NOTICE file distributed with
 *  this work for additional information regarding copyright ownership.
 *  The ASF licenses this file to You under the Apache License, Version 2.0
 *  (the "License"); you may not use this file except in compliance with
 *  the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.apache.hadoop.fs.s3a.s3guard;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * LinkedHashMap that implements a maximum size and LRU eviction policy.
 */
public class LruHashMap<K, V> extends LinkedHashMap<K, V> {
  private final int maxSize;
  public LruHashMap(int initialCapacity, int maxSize) {
    super(initialCapacity);
    this.maxSize = maxSize;
  }

  @Override
  protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
    return size() > maxSize;
  }

  /**
   * get() plus side-effect of making the element Most Recently Used.
   * @param key lookup key
   * @return value
   */

  public V mruGet(K key) {
    V val = remove(key);
    if (val != null) {
      put(key, val);
    }
    return val;
  }
}
